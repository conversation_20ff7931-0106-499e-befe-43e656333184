"use client";

import { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  User,
  FileText,
  Settings,
  LogOut,
  ChevronDown,
  Briefcase,
  Heart,
} from "lucide-react";
import { useAuthStore } from "@/stores/user-store";
import type { User as UserType } from "@/types/user";

interface UserDropdownProps {
  user: UserType;
  className?: string;
}

export function UserDropdown({ user, className = "" }: UserDropdownProps) {
  const router = useRouter();
  const { logout } = useAuthStore();
  const [isLoading, setIsLoading] = useState(false);

  const handleLogout = async () => {
    try {
      setIsLoading(true);
      await logout();
      router.push("/");
    } catch (error) {
      console.error("Logout failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Get user display name
  const displayName =
    user.profile?.firstName && user.profile?.lastName
      ? `${user.profile.firstName} ${user.profile.lastName}`
      : user.name || user.email;

  // Get user initials for avatar fallback
  const initials = displayName
    .split(" ")
    .map((name) => name.charAt(0))
    .join("")
    .toUpperCase()
    .slice(0, 2);

  // Role-based menu items
  const getMenuItems = () => {
    const commonItems = [
      { href: "/profile", icon: User, label: "Profile" },
      { href: "/settings", icon: Settings, label: "Settings" },
    ];

    if (user.role === "job_seeker") {
      return [
        ...commonItems,
        { href: "/my-applications", icon: FileText, label: "My Applications" },
        { href: "/saved-jobs", icon: Heart, label: "Saved Jobs" },
      ];
    }

    if (user.role === "employer") {
      return [
        ...commonItems,
        { href: "/my-jobs", icon: Briefcase, label: "My Job Posts" },
        { href: "/candidates", icon: User, label: "Candidates" },
      ];
    }

    return commonItems;
  };

  return (
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger
        className={`flex items-center space-x-2 text-[#202124] hover:text-[#1967D2] transition-colors focus:outline-none min-w-0 ${className}`}
      >
        <Avatar className="w-8 h-8 flex-shrink-0">
          <AvatarImage src={user.avatar} alt={displayName} />
          <AvatarFallback className="bg-[#1967D2] text-white text-sm">
            {initials}
          </AvatarFallback>
        </Avatar>
        <span className="font-['Jost',sans-serif] text-[15px] truncate min-w-0 max-w-[100px] sm:max-w-[120px] md:max-w-[140px] lg:max-w-[160px]">
          {displayName}
        </span>
        <ChevronDown className="w-3 h-3 flex-shrink-0" />
      </DropdownMenuTrigger>

      <DropdownMenuContent
        sideOffset={12}
        align="end"
        avoidCollisions={true}
        collisionPadding={8}
        className="w-56"
      >
        {/* User Info Header */}
        <div className="px-3 py-2 border-b">
          <p className="font-medium text-sm text-gray-900">{displayName}</p>
          <p className="text-xs text-gray-500">{user.email}</p>
          <p className="text-xs text-blue-600 capitalize">
            {user.role.replace("_", " ")}
          </p>
        </div>

        {/* Menu Items */}
        {getMenuItems().map((item) => (
          <DropdownMenuItem key={item.href} asChild>
            <Link
              href={item.href}
              className="flex items-center space-x-2 cursor-pointer"
            >
              <item.icon className="w-4 h-4" />
              <span>{item.label}</span>
            </Link>
          </DropdownMenuItem>
        ))}

        <DropdownMenuSeparator />

        {/* Logout */}
        <DropdownMenuItem
          onClick={handleLogout}
          disabled={isLoading}
          className="flex items-center space-x-2 text-red-600 focus:text-red-600 cursor-pointer"
        >
          <LogOut className="w-4 h-4" />
          <span>{isLoading ? "Logging out..." : "Logout"}</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
