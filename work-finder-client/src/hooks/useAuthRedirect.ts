"use client";

import { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAuthStore } from "@/stores/user-store";

/**
 * Hook to handle authentication-based redirects
 * Redirects authenticated job seekers away from auth pages
 */
export function useAuthRedirect() {
  const router = useRouter();
  const pathname = usePathname();
  const { user, isAuthenticated } = useAuthStore();

  useEffect(() => {
    // Don't redirect if not authenticated
    if (!isAuthenticated || !user) return;

    // Pages that authenticated users shouldn't access
    const authPages = ["/login", "/register", "/forgot-password", "/reset-password"];
    
    // Check if current page is an auth page
    const isAuthPage = authPages.some(page => pathname.startsWith(page));
    
    if (isAuthPage) {
      // Redirect job seekers to home page
      if (user.role === "job_seeker") {
        router.replace("/");
        return;
      }
      
      // Redirect employers to their dashboard
      if (user.role === "employer") {
        router.replace("/employer/dashboard");
        return;
      }
      
      // Redirect admins to admin panel
      if (user.role === "admin") {
        router.replace("/admin");
        return;
      }
    }
  }, [isAuthenticated, user, pathname, router]);
}

/**
 * Hook to get user authentication status and role
 */
export function useAuth() {
  const { user, isAuthenticated, isLoading } = useAuthStore();
  
  return {
    user,
    isAuthenticated,
    isLoading,
    isJobSeeker: user?.role === "job_seeker",
    isEmployer: user?.role === "employer",
    isAdmin: user?.role === "admin",
  };
}
